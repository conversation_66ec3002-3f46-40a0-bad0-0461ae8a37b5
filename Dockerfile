FROM ubuntu:24.04

ENV DEBIAN_FRONTEND=noninteractive
ENV PATH="/usr/local/go/bin:/go/bin:$PATH"
ENV GOPATH=/go

RUN apt-get update && apt-get install -y curl wget git build-essential jq gnupg ca-certificates lsb-release

# Node.js 20
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs

# Go 1.22
RUN wget https://go.dev/dl/go1.22.0.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf go1.22.0.linux-amd64.tar.gz && \
    rm go1.22.0.linux-amd64.tar.gz

WORKDIR /app
COPY . .

# Ensure ethermintd is executable and in PATH
RUN mkdir -p /go/bin && \
    chmod +x ./ethermint/ethermintd && \
    cp ./ethermint/ethermintd /go/bin/ethermintd && \
    cp ./ethermint/ethermintd /usr/local/bin/ethermintd

# Verify ethermintd works
RUN ethermintd version

RUN chmod +x ./ethermint/init.sh
RUN chmod +x start.sh && cp start.sh /start.sh