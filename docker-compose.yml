version: '3.8'

services:
  ethermint:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ethermintd
    volumes:
      - ethermint_data:/root/.ethermintd
      - ./dapp:/app/dapp
    ports:
      - "26657:26657"
      - "26656:26656"
      - "8545:8545"
      - "8546:8546"
    command: /start.sh

  dapp:
    build:
      context: .
    container_name: dapp
    ports:
      - "3000:3000"
    volumes:
      - ./dapp:/app
    working_dir: /app
    command: sh -c "npm install && chmod +x node_modules/.bin/next && npm run build && npm start"

    depends_on:
      - ethermint
volumes:
  ethermint_data:

